{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Analysis with Pandas\n", "\n", "This notebook demonstrates basic data analysis using pandas in the conda environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "\n", "print(\"Pandas version:\", pd.__version__)\n", "print(\"NumPy version:\", np.__version__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a sample DataFrame\n", "data = {\n", "    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],\n", "    'Age': [25, 30, 35, 28, 32],\n", "    'City': ['New York', 'London', 'Tokyo', 'Paris', 'Sydney'],\n", "    'Salary': [50000, 60000, 70000, 55000, 65000]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "print(\"Sample DataFrame:\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic DataFrame operations\n", "print(\"DataFrame Info:\")\n", "print(df.info())\n", "print(\"\\nDataFrame Description:\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data filtering and selection\n", "print(\"People older than 30:\")\n", "older_than_30 = df[df['Age'] > 30]\n", "print(older_than_30)\n", "\n", "print(\"\\nAverage salary:\")\n", "print(f\"${df['Salary'].mean():,.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group by operations\n", "print(\"Salary statistics by age group:\")\n", "df['Age_Group'] = pd.cut(df['Age'], bins=[20, 30, 40], labels=['20-30', '30-40'])\n", "salary_by_age = df.groupby('Age_Group')['Salary'].agg(['mean', 'min', 'max'])\n", "print(salary_by_age)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}